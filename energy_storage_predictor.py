import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

class EnergyStoragePredictor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.models = {
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'Linear Regression': LinearRegression()
        }
        self.best_model = None
        self.feature_names = []
        
    def load_data(self):
        """加载和合并数据"""
        print("正在加载数据...")
        
        # 加载元素门捷列夫数数据
        elements_df = pd.read_excel('elements_data.xlsx')
        mendeleev_dict = dict(zip(elements_df['Symbol'], elements_df['Mendeleev_Number']))
        
        # 加载A位和B位元素数据
        a_df = pd.read_excel('A_site_elements_fully_expanded.xlsx')
        b_df = pd.read_excel('B_site_elements_fully_expanded.xlsx')
        
        # 合并数据（基于No.1列）
        merged_df = pd.merge(a_df, b_df, on=['No.1', 'DOI', 'component', 'E (kV cm-1)', 'Wrec (J cm-3)', 'η'])
        
        print(f"合并后数据形状: {merged_df.shape}")
        return merged_df, mendeleev_dict
    
    def calculate_mixing_entropy(self, elements, contents):
        """计算混合熵"""
        if not elements or not contents:
            return 0.0

        # 确保contents是数值类型且归一化
        valid_contents = []
        for content in contents:
            try:
                c = float(content)
                if c > 0:
                    valid_contents.append(c)
            except (ValueError, TypeError):
                continue

        if not valid_contents:
            return 0.0

        # 归一化
        total = sum(valid_contents)
        if total <= 0:
            return 0.0

        fractions = [c / total for c in valid_contents]

        # 计算混合熵: S_mix = -R * Σ(x_i * ln(x_i))
        # 这里使用简化版本，不包含气体常数R
        entropy = 0.0
        for fraction in fractions:
            if fraction > 0:
                entropy -= fraction * np.log(fraction)

        return entropy

    def extract_features(self, df, mendeleev_dict):
        """提取特征"""
        print("正在提取特征...")

        features = []

        for idx, row in df.iterrows():
            feature_row = {}

            # 特征1: 测试电场
            feature_row['electric_field'] = row['E (kV cm-1)']

            # A位元素特征
            a_elements = []
            a_contents = []
            for i in range(1, 8):  # A_element_1 到 A_element_7
                element_col = f'A_element_{i}'
                content_col = f'A_content_{i}'
                if element_col in row and pd.notna(row[element_col]):
                    element = str(row[element_col]).strip()
                    # 清理元素符号（去除化学价）
                    clean_element = element.split('⁺')[0].split('²')[0].split('³')[0].split('⁴')[0].split('⁵')[0]
                    if clean_element in mendeleev_dict:
                        a_elements.append(mendeleev_dict[clean_element])
                        content = row[content_col] if pd.notna(row[content_col]) else 0
                        # 确保content是数值类型
                        try:
                            content = float(content)
                        except (ValueError, TypeError):
                            content = 0.0
                        a_contents.append(content)

            # B位元素特征
            b_elements = []
            b_contents = []
            for i in range(1, 6):  # B_element_1 到 B_element_5
                element_col = f'B_element_{i}'
                content_col = f'B_content_{i}'
                if element_col in row and pd.notna(row[element_col]):
                    element = str(row[element_col]).strip()
                    # 清理元素符号（去除化学价）
                    clean_element = element.split('⁺')[0].split('²')[0].split('³')[0].split('⁴')[0].split('⁵')[0]
                    if clean_element in mendeleev_dict:
                        b_elements.append(mendeleev_dict[clean_element])
                        content = row[content_col] if pd.notna(row[content_col]) else 0
                        # 确保content是数值类型
                        try:
                            content = float(content)
                        except (ValueError, TypeError):
                            content = 0.0
                        b_contents.append(content)

            # 特征2: A位加权平均门捷列夫数
            if a_elements and a_contents:
                total_a_content = sum(a_contents)
                if total_a_content > 0:
                    feature_row['A_weighted_mendeleev'] = sum(m*c for m, c in zip(a_elements, a_contents)) / total_a_content
                else:
                    feature_row['A_weighted_mendeleev'] = np.mean(a_elements) if a_elements else 0
            else:
                feature_row['A_weighted_mendeleev'] = 0

            # 特征3: B位加权平均门捷列夫数
            if b_elements and b_contents:
                total_b_content = sum(b_contents)
                if total_b_content > 0:
                    feature_row['B_weighted_mendeleev'] = sum(m*c for m, c in zip(b_elements, b_contents)) / total_b_content
                else:
                    feature_row['B_weighted_mendeleev'] = np.mean(b_elements) if b_elements else 0
            else:
                feature_row['B_weighted_mendeleev'] = 0

            # 特征4: A位混合熵
            feature_row['A_mixing_entropy'] = self.calculate_mixing_entropy(a_elements, a_contents)

            # 特征5: B位混合熵
            feature_row['B_mixing_entropy'] = self.calculate_mixing_entropy(b_elements, b_contents)

            # 目标变量
            feature_row['energy_density'] = row['Wrec (J cm-3)']
            feature_row['energy_efficiency'] = row['η']

            # 用于分组的标识符（同一文献同一化学成分）
            feature_row['group_id'] = f"{row['DOI']}_{row['component']}"

            features.append(feature_row)

        feature_df = pd.DataFrame(features)

        # 移除包含NaN的行
        feature_df = feature_df.dropna()

        print(f"提取特征后数据形状: {feature_df.shape}")
        print(f"使用的特征: 测试电场, A位门捷列夫数, B位门捷列夫数, A位混合熵, B位混合熵")
        return feature_df
    
    def split_data_by_group(self, df, test_size=0.2, val_size=0.1):
        """按组分割数据，避免数据泄露"""
        print("正在按组分割数据...")

        # 准备特征和目标变量
        feature_cols = ['electric_field', 'A_weighted_mendeleev', 'B_weighted_mendeleev', 'A_mixing_entropy', 'B_mixing_entropy']
        X = df[feature_cols]
        y_density = df['energy_density']
        y_efficiency = df['energy_efficiency']
        groups = df['group_id']

        self.feature_names = feature_cols

        # 获取所有唯一的组
        unique_groups = groups.unique()
        np.random.seed(42)
        shuffled_groups = np.random.permutation(unique_groups)

        # 计算分割点
        n_groups = len(unique_groups)
        test_groups_count = int(n_groups * test_size)
        val_groups_count = int(n_groups * val_size)

        # 分割组
        test_groups = set(shuffled_groups[:test_groups_count])
        val_groups = set(shuffled_groups[test_groups_count:test_groups_count + val_groups_count])
        train_groups = set(shuffled_groups[test_groups_count + val_groups_count:])

        # 根据组分割数据
        train_mask = groups.isin(train_groups)
        val_mask = groups.isin(val_groups)
        test_mask = groups.isin(test_groups)

        X_train = X[train_mask]
        X_val = X[val_mask]
        X_test = X[test_mask]

        y_density_train = y_density[train_mask]
        y_density_val = y_density[val_mask]
        y_density_test = y_density[test_mask]

        y_efficiency_train = y_efficiency[train_mask]
        y_efficiency_val = y_efficiency[val_mask]
        y_efficiency_test = y_efficiency[test_mask]

        print(f"训练集大小: {X_train.shape[0]}")
        print(f"验证集大小: {X_val.shape[0]}")
        print(f"测试集大小: {X_test.shape[0]}")

        print(f"训练集组数: {len(train_groups)}")
        print(f"验证集组数: {len(val_groups)}")
        print(f"测试集组数: {len(test_groups)}")
        print(f"训练-验证组重叠: {len(train_groups & val_groups)}")
        print(f"训练-测试组重叠: {len(train_groups & test_groups)}")
        print(f"验证-测试组重叠: {len(val_groups & test_groups)}")

        # 验证分割的正确性
        assert len(train_groups & val_groups) == 0, "训练集和验证集有组重叠！"
        assert len(train_groups & test_groups) == 0, "训练集和测试集有组重叠！"
        assert len(val_groups & test_groups) == 0, "验证集和测试集有组重叠！"

        return (X_train, X_val, X_test,
                y_density_train, y_density_val, y_density_test,
                y_efficiency_train, y_efficiency_val, y_efficiency_test)

    def train_models(self, X_train, X_val, y_train, y_val, target_name):
        """训练多个模型并选择最佳模型"""
        print(f"\n正在训练{target_name}预测模型...")

        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)

        best_score = -np.inf
        best_model_name = None
        results = {}

        for name, model in self.models.items():
            print(f"训练 {name}...")

            # 训练模型
            if name == 'Linear Regression':
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_val_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_val)

            # 评估模型
            mse = mean_squared_error(y_val, y_pred)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(y_val, y_pred)
            r2 = r2_score(y_val, y_pred)

            results[name] = {
                'model': model,
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'predictions': y_pred
            }

            print(f"  RMSE: {rmse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")

            # 选择最佳模型（基于R²分数）
            if r2 > best_score:
                best_score = r2
                best_model_name = name

        print(f"\n最佳模型: {best_model_name} (R² = {best_score:.4f})")
        return results, best_model_name

    def evaluate_model(self, model, X_test, y_test, model_name, target_name, use_scaling=False):
        """评估模型在测试集上的性能"""
        print(f"\n评估{target_name}预测模型 ({model_name}) 在测试集上的性能...")

        if use_scaling:
            X_test_scaled = self.scaler.transform(X_test)
            y_pred = model.predict(X_test_scaled)
        else:
            y_pred = model.predict(X_test)

        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        print(f"测试集性能:")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  MAE: {mae:.4f}")
        print(f"  R²: {r2:.4f}")

        return {'rmse': rmse, 'mae': mae, 'r2': r2, 'predictions': y_pred}

    def plot_results(self, y_true, y_pred, title, target_name):
        """绘制预测结果"""
        plt.figure(figsize=(12, 10))

        # 设置中文标签映射
        if 'density' in target_name.lower() or '密度' in target_name:
            target_label = 'Energy Density (J/cm³)'
            target_cn = '储能密度'
        else:
            target_label = 'Energy Efficiency (%)'
            target_cn = '储能效率'

        # 预测vs真实值散点图
        plt.subplot(2, 2, 1)
        plt.scatter(y_true, y_pred, alpha=0.6, s=20)
        plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        plt.xlabel(f'True {target_label}', fontsize=12)
        plt.ylabel(f'Predicted {target_label}', fontsize=12)
        plt.title(f'{target_cn}预测 - Prediction vs True Values', fontsize=14)
        plt.grid(True, alpha=0.3)

        # 残差图
        plt.subplot(2, 2, 2)
        residuals = y_true - y_pred
        plt.scatter(y_pred, residuals, alpha=0.6, s=20)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel(f'Predicted {target_label}', fontsize=12)
        plt.ylabel('Residuals', fontsize=12)
        plt.title(f'{target_cn}预测 - Residual Plot', fontsize=14)
        plt.grid(True, alpha=0.3)

        # 残差直方图
        plt.subplot(2, 2, 3)
        plt.hist(residuals, bins=30, alpha=0.7, edgecolor='black')
        plt.xlabel('Residuals', fontsize=12)
        plt.ylabel('Frequency', fontsize=12)
        plt.title(f'{target_cn}预测 - Residual Distribution', fontsize=14)
        plt.grid(True, alpha=0.3)

        # 特征重要性（如果是树模型）
        plt.subplot(2, 2, 4)
        if hasattr(self.best_model, 'feature_importances_'):
            importances = self.best_model.feature_importances_
            indices = np.argsort(importances)[::-1]

            # 特征名称映射
            feature_name_map = {
                'electric_field': 'Electric Field',
                'A_weighted_mendeleev': 'A-site Mendeleev',
                'B_weighted_mendeleev': 'B-site Mendeleev',
                'A_mixing_entropy': 'A-site Entropy',
                'B_mixing_entropy': 'B-site Entropy'
            }

            feature_labels = [feature_name_map.get(self.feature_names[i], self.feature_names[i]) for i in indices]

            plt.bar(range(len(importances)), importances[indices])
            plt.xticks(range(len(importances)), feature_labels, rotation=45, ha='right')
            plt.ylabel('Importance', fontsize=12)
            plt.title(f'{target_cn}预测 - Feature Importance', fontsize=14)
            plt.grid(True, alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'Feature Importance\nNot Available\nfor This Model',
                    ha='center', va='center', transform=plt.gca().transAxes, fontsize=12)
            plt.title(f'{target_cn}预测 - Feature Importance', fontsize=14)

        plt.tight_layout()

        # 保存文件名使用英文
        safe_filename = target_cn.replace('储能密度', 'energy_density').replace('储能效率', 'energy_efficiency')
        plt.savefig(f'{safe_filename}_prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()

    def run_prediction(self):
        """运行完整的预测流程"""
        # 加载数据
        df, mendeleev_dict = self.load_data()

        # 提取特征
        feature_df = self.extract_features(df, mendeleev_dict)

        # 分割数据
        (X_train, X_val, X_test,
         y_density_train, y_density_val, y_density_test,
         y_efficiency_train, y_efficiency_val, y_efficiency_test) = self.split_data_by_group(feature_df)

        # 预测储能密度
        print("\n" + "="*60)
        print("储能密度预测")
        print("="*60)
        density_results, best_density_model = self.train_models(
            X_train, X_val, y_density_train, y_density_val, "储能密度"
        )

        # 评估储能密度模型
        best_density_model_obj = density_results[best_density_model]['model']
        use_scaling_density = best_density_model == 'Linear Regression'
        density_test_results = self.evaluate_model(
            best_density_model_obj, X_test, y_density_test,
            best_density_model, "储能密度", use_scaling_density
        )

        # 预测储能效率
        print("\n" + "="*60)
        print("储能效率预测")
        print("="*60)
        efficiency_results, best_efficiency_model = self.train_models(
            X_train, X_val, y_efficiency_train, y_efficiency_val, "储能效率"
        )

        # 评估储能效率模型
        best_efficiency_model_obj = efficiency_results[best_efficiency_model]['model']
        use_scaling_efficiency = best_efficiency_model == 'Linear Regression'
        efficiency_test_results = self.evaluate_model(
            best_efficiency_model_obj, X_test, y_efficiency_test,
            best_efficiency_model, "储能效率", use_scaling_efficiency
        )

        # 绘制结果
        self.best_model = best_density_model_obj
        self.plot_results(y_density_test, density_test_results['predictions'],
                         f"储能密度预测 ({best_density_model})", "储能密度")

        self.best_model = best_efficiency_model_obj
        self.plot_results(y_efficiency_test, efficiency_test_results['predictions'],
                         f"储能效率预测 ({best_efficiency_model})", "储能效率")

        return {
            'density_results': density_test_results,
            'efficiency_results': efficiency_test_results,
            'best_density_model': best_density_model,
            'best_efficiency_model': best_efficiency_model
        }

if __name__ == "__main__":
    predictor = EnergyStoragePredictor()
    results = predictor.run_prediction()

    print("\n" + "="*60)
    print("最终结果总结")
    print("="*60)
    print(f"储能密度预测最佳模型: {results['best_density_model']}")
    print(f"  测试集R²: {results['density_results']['r2']:.4f}")
    print(f"  测试集RMSE: {results['density_results']['rmse']:.4f}")

    print(f"\n储能效率预测最佳模型: {results['best_efficiency_model']}")
    print(f"  测试集R²: {results['efficiency_results']['r2']:.4f}")
    print(f"  测试集RMSE: {results['efficiency_results']['rmse']:.4f}")
