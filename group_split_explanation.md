# 按组分割实现机制详解

## 问题背景

在储能材料研究中，同一篇文献通常会报告同一种化学成分在不同电场强度下的测试结果。如果简单地随机分割数据，可能会导致：
- 训练集包含某种材料在100kV/cm下的数据
- 测试集包含同一种材料在200kV/cm下的数据

这会造成**数据泄露**，因为模型可能学会了该材料的特性，从而在测试时获得不真实的高性能。

## 解决方案：按组分割

### 1. 组ID构建

```python
# 在extract_features函数中
feature_row['group_id'] = f"{row['DOI']}_{row['component']}"
```

**组ID格式**: `DOI_化学成分`

**示例**:
- `10.1016/j.jallcom.2024.176753_0.9(0.6Bi0.5Na0.5TiO3-0.4SrTiO3)-0.1NaNbO3`
- `10.1016/j.ceramint.2024.02.382_0.8(0.91Na0.5Bi0.5TiO3-0.09K0.7La0.1NbO3)-0.2BaMg1/3Ta2/3O3`

### 2. 分割实现步骤

#### 步骤1: 获取所有唯一组
```python
unique_groups = groups.unique()  # 629个唯一组
```

#### 步骤2: 随机打乱组
```python
np.random.seed(42)
shuffled_groups = np.random.permutation(unique_groups)
```

#### 步骤3: 按比例分配组
```python
n_groups = len(unique_groups)
test_groups_count = int(n_groups * 0.2)    # 20% = 125组
val_groups_count = int(n_groups * 0.1)     # 10% = 62组
train_groups_count = n_groups - test_groups_count - val_groups_count  # 442组

test_groups = set(shuffled_groups[:test_groups_count])
val_groups = set(shuffled_groups[test_groups_count:test_groups_count + val_groups_count])
train_groups = set(shuffled_groups[test_groups_count + val_groups_count:])
```

#### 步骤4: 根据组分配样本
```python
train_mask = groups.isin(train_groups)
val_mask = groups.isin(val_groups)
test_mask = groups.isin(test_groups)

X_train = X[train_mask]    # 1590个样本
X_val = X[val_mask]        # 287个样本  
X_test = X[test_mask]      # 447个样本
```

### 3. 关键保证

#### 组完整性保证
- 每个组的**所有样本**都被分配到**同一个集合**
- 例如：组`10.1016/j.jallcom.2024.176753_0.9(0.6Bi0.5Na0.5TiO3-0.4SrTiO3)-0.1NaNbO3`包含40个样本（电场从10-400kV/cm），这40个样本**全部**在训练集中

#### 无数据泄露保证
- 训练-验证组重叠: **0**
- 训练-测试组重叠: **0**  
- 验证-测试组重叠: **0**

## 具体例子

### 示例组数据
**组ID**: `10.1016/j.jallcom.2024.176753_0.9(0.6Bi0.5Na0.5TiO3-0.4SrTiO3)-0.1NaNbO3`

**包含40个样本**，电场范围10-400kV/cm：

| 电场(kV/cm) | 储能密度(J/cm³) | 储能效率(%) |
|------------|----------------|-------------|
| 10.0       | 0.50           | 96.74       |
| 20.0       | 0.53           | 98.11       |
| ...        | ...            | ...         |
| 400.0      | 5.86           | 91.59       |

**分配结果**: 该组被分配到训练集，所有40个样本都在训练集中。

### 避免的问题
❌ **错误分割**（会导致数据泄露）:
- 训练集: 该材料在10-200kV/cm的数据
- 测试集: 该材料在210-400kV/cm的数据

✅ **正确分割**（按组分割）:
- 训练集: 该材料的所有数据（10-400kV/cm）
- 测试集: 完全不同的材料组合

## 技术优势

1. **完全避免数据泄露**: 测试集中的材料在训练时完全未见过
2. **真实性能评估**: 模型必须学会材料的通用规律，而不是记忆特定材料
3. **可重现性**: 固定随机种子确保分割结果一致
4. **科学严谨性**: 符合机器学习最佳实践

## 验证结果

- ✅ 总样本数保持不变: 2324个
- ✅ 所有组都被正确分配: 629个组
- ✅ 无组重叠: 0个重叠组
- ✅ 组完整性: 每个组的所有样本都在同一集合中

这种实现确保了模型评估的科学性和可靠性，避免了因数据泄露导致的过度乐观的性能估计。
