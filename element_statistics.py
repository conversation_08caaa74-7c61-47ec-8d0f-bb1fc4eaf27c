import pandas as pd
import numpy as np
from collections import Counter
import os

def count_elements_in_site(df, site_prefix):
    """统计指定位点的元素种类"""
    print(f"\n统计{site_prefix}位元素:")

    all_elements = []
    element_counts = Counter()

    # 找到所有以site_prefix开头的element列
    element_columns = [col for col in df.columns if col.startswith(f'{site_prefix}_element_')]
    print(f"找到元素列: {element_columns}")

    # 遍历每个元素列
    for col in element_columns:
        elements_in_col = df[col].dropna().unique()
        print(f"\n列 {col} 中的元素:")
        for element in elements_in_col:
            if pd.notna(element) and element != '':
                count = df[col].value_counts()[element]
                all_elements.append(element)
                element_counts[element] += count
                print(f"  {element}: {count}次")

    # 获取唯一元素
    unique_elements = sorted(set(all_elements))

    print(f"\n{site_prefix}位发现的元素种类 ({len(unique_elements)}种):")
    for element in unique_elements:
        print(f"  {element}: {element_counts[element]}次")

    return unique_elements, element_counts

def main():
    """主函数"""
    # 文件路径
    a_site_file = "A_site_elements_fully_expanded.xlsx"
    b_site_file = "B_site_elements_fully_expanded.xlsx"

    # 检查文件是否存在
    if not os.path.exists(a_site_file):
        print(f"文件不存在: {a_site_file}")
        return
    if not os.path.exists(b_site_file):
        print(f"文件不存在: {b_site_file}")
        return

    print("=" * 60)
    print("A位和B位元素统计分析")
    print("=" * 60)

    # 读取A位元素文件
    print(f"读取文件: {a_site_file}")
    a_df = pd.read_excel(a_site_file)
    print(f"A位文件形状: {a_df.shape}")
    a_elements, a_counts = count_elements_in_site(a_df, "A")

    print("\n" + "=" * 60)

    # 读取B位元素文件
    print(f"读取文件: {b_site_file}")
    b_df = pd.read_excel(b_site_file)
    print(f"B位文件形状: {b_df.shape}")
    b_elements, b_counts = count_elements_in_site(b_df, "B")
    
    # 比较分析
    if 'a_elements' in locals() and 'b_elements' in locals():
        print("\n" + "=" * 60)
        print("比较分析")
        print("=" * 60)
        
        a_set = set(a_elements)
        b_set = set(b_elements)
        
        print(f"A位元素总数: {len(a_set)}")
        print(f"B位元素总数: {len(b_set)}")
        
        common_elements = a_set & b_set
        a_only = a_set - b_set
        b_only = b_set - a_set
        
        print(f"\n共同元素 ({len(common_elements)}种): {sorted(common_elements)}")
        print(f"仅在A位的元素 ({len(a_only)}种): {sorted(a_only)}")
        print(f"仅在B位的元素 ({len(b_only)}种): {sorted(b_only)}")
        
        # 保存结果到文件
        with open("element_analysis_results.txt", "w", encoding="utf-8") as f:
            f.write("A位和B位元素统计分析结果\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"A位元素 ({len(a_set)}种):\n")
            f.write(", ".join(sorted(a_set)) + "\n\n")
            f.write(f"B位元素 ({len(b_set)}种):\n")
            f.write(", ".join(sorted(b_set)) + "\n\n")
            f.write(f"共同元素 ({len(common_elements)}种):\n")
            f.write(", ".join(sorted(common_elements)) + "\n\n")
            f.write(f"仅在A位的元素 ({len(a_only)}种):\n")
            f.write(", ".join(sorted(a_only)) + "\n\n")
            f.write(f"仅在B位的元素 ({len(b_only)}种):\n")
            f.write(", ".join(sorted(b_only)) + "\n")
        
        print(f"\n结果已保存到: element_analysis_results.txt")

if __name__ == "__main__":
    main()
