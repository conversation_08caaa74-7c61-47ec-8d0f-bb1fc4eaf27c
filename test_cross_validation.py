#!/usr/bin/env python3
"""
测试十折交叉验证的实现
"""

from energy_storage_predictor import EnergyStoragePredictor
import pandas as pd
import numpy as np

def test_cross_validation():
    """测试十折交叉验证"""
    print("="*60)
    print("测试十折交叉验证实现")
    print("="*60)
    
    # 创建预测器实例
    predictor = EnergyStoragePredictor()
    
    # 运行完整预测（包含十折交叉验证）
    results = predictor.run_prediction()
    
    print(f"\n" + "="*60)
    print("十折交叉验证结果分析")
    print("="*60)
    
    # 分析储能密度的交叉验证结果
    print(f"储能密度预测:")
    print(f"  最佳模型: {results['best_density_model']}")
    
    density_cv = results['density_cv_results'][results['best_density_model']]
    print(f"  十折交叉验证R²: {density_cv['mean_r2']:.4f} (±{density_cv['std_r2']:.4f})")
    print(f"  各折R²分数: {[f'{score:.4f}' for score in density_cv['cv_scores']]}")
    print(f"  测试集R²: {results['density_results']['r2']:.4f}")
    
    # 分析储能效率的交叉验证结果
    print(f"\n储能效率预测:")
    print(f"  最佳模型: {results['best_efficiency_model']}")
    
    efficiency_cv = results['efficiency_cv_results'][results['best_efficiency_model']]
    print(f"  十折交叉验证R²: {efficiency_cv['mean_r2']:.4f} (±{efficiency_cv['std_r2']:.4f})")
    print(f"  各折R²分数: {[f'{score:.4f}' for score in efficiency_cv['cv_scores']]}")
    print(f"  测试集R²: {results['efficiency_results']['r2']:.4f}")
    
    # 比较所有模型的交叉验证结果
    print(f"\n" + "="*60)
    print("所有模型的十折交叉验证比较")
    print("="*60)
    
    print(f"储能密度预测模型比较:")
    for model_name, cv_result in results['density_cv_results'].items():
        print(f"  {model_name}: {cv_result['mean_r2']:.4f} (±{cv_result['std_r2']:.4f})")
    
    print(f"\n储能效率预测模型比较:")
    for model_name, cv_result in results['efficiency_cv_results'].items():
        print(f"  {model_name}: {cv_result['mean_r2']:.4f} (±{cv_result['std_r2']:.4f})")
    
    # 分析交叉验证的稳定性
    print(f"\n" + "="*60)
    print("交叉验证稳定性分析")
    print("="*60)
    
    print(f"储能密度预测稳定性:")
    for model_name, cv_result in results['density_cv_results'].items():
        cv_coefficient = cv_result['std_r2'] / abs(cv_result['mean_r2']) if cv_result['mean_r2'] != 0 else float('inf')
        stability = "稳定" if cv_coefficient < 0.1 else "一般" if cv_coefficient < 0.2 else "不稳定"
        print(f"  {model_name}: 变异系数={cv_coefficient:.3f} ({stability})")
    
    print(f"\n储能效率预测稳定性:")
    for model_name, cv_result in results['efficiency_cv_results'].items():
        cv_coefficient = cv_result['std_r2'] / abs(cv_result['mean_r2']) if cv_result['mean_r2'] != 0 else float('inf')
        stability = "稳定" if cv_coefficient < 0.1 else "一般" if cv_coefficient < 0.2 else "不稳定"
        print(f"  {model_name}: 变异系数={cv_coefficient:.3f} ({stability})")
    
    return results

def analyze_cv_vs_test_performance(results):
    """分析交叉验证性能与测试集性能的一致性"""
    print(f"\n" + "="*60)
    print("交叉验证 vs 测试集性能一致性分析")
    print("="*60)
    
    # 储能密度
    density_cv_r2 = results['density_cv_results'][results['best_density_model']]['mean_r2']
    density_test_r2 = results['density_results']['r2']
    density_diff = abs(density_cv_r2 - density_test_r2)
    
    print(f"储能密度预测:")
    print(f"  交叉验证R²: {density_cv_r2:.4f}")
    print(f"  测试集R²: {density_test_r2:.4f}")
    print(f"  差异: {density_diff:.4f}")
    print(f"  一致性: {'良好' if density_diff < 0.05 else '一般' if density_diff < 0.1 else '较差'}")
    
    # 储能效率
    efficiency_cv_r2 = results['efficiency_cv_results'][results['best_efficiency_model']]['mean_r2']
    efficiency_test_r2 = results['efficiency_results']['r2']
    efficiency_diff = abs(efficiency_cv_r2 - efficiency_test_r2)
    
    print(f"\n储能效率预测:")
    print(f"  交叉验证R²: {efficiency_cv_r2:.4f}")
    print(f"  测试集R²: {efficiency_test_r2:.4f}")
    print(f"  差异: {efficiency_diff:.4f}")
    print(f"  一致性: {'良好' if efficiency_diff < 0.05 else '一般' if efficiency_diff < 0.1 else '较差'}")

if __name__ == "__main__":
    # 测试十折交叉验证
    results = test_cross_validation()
    
    # 分析性能一致性
    analyze_cv_vs_test_performance(results)
    
    print(f"\n" + "="*60)
    print("十折交叉验证测试完成！")
    print("="*60)
    print("优势:")
    print("1. 更可靠的模型性能评估")
    print("2. 减少过拟合风险")
    print("3. 更好的模型选择")
    print("4. 基于组的分割避免数据泄露")
    print("5. 提供性能稳定性指标")
    print("="*60)
